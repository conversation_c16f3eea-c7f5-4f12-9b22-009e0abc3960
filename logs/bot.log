2025-07-10 12:52:55,770 - __main__ - WARNING - Slow command execution: watchlist took 7.91s
2025-07-10 12:53:26,643 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e240a40364a7f4db8f953140d0c43980e79401ecaeeeda222d06d89c89bb033e
2025-07-10 12:53:28,763 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 5 messages from #trade (keep_pinned: True)
2025-07-10 13:06:02,648 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a7777413d6a2ca6d9cf058327b8040a6bb08555560f6e1045d6d983d41820277
2025-07-10 17:56:02,046 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7aa737879f9d3e327886b528a31ea00880e9a3f80fde1746cf5d8fdcf8002325
2025-07-10 18:14:26,218 - services.market.market_service - ERROR - Error fetching OHLCV data for BTCUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=4h&limit=21&symbol=BTCUSDT
2025-07-10 18:50:30,771 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-10 18:50:31,156 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-10 19:26:32,682 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-10 19:26:33,062 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-10 21:18:27,174 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=13355a92ce9018d1abcef8f71956524c92eaf00ca5350df84c28786167ba2fc7
2025-07-10 21:19:26,761 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=764ff0f6216710f850fef000f5604f0164bddbab5bb33beebb06ae457a1ef149
2025-07-10 21:19:26,764 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=764ff0f6216710f850fef000f5604f0164bddbab5bb33beebb06ae457a1ef149 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=764ff0f6216710f850fef000f5604f0164bddbab5bb33beebb06ae457a1ef149)
2025-07-10 21:30:57,899 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=857d40230b63e822d0f47c26965ea3c020b904376405aee2e4dc449afbcd37e2
2025-07-10 21:30:57,901 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=857d40230b63e822d0f47c26965ea3c020b904376405aee2e4dc449afbcd37e2 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=857d40230b63e822d0f47c26965ea3c020b904376405aee2e4dc449afbcd37e2)
2025-07-10 21:46:36,412 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-10 22:06:29,884 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=e0768fe8064197994e8fface9320b8f751479f430511e6ab79822e02a75b3c03
2025-07-10 22:22:34,611 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-10 22:22:34,921 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-10 22:48:17,433 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-10 22:48:17,810 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-10 23:06:56,676 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=af116e4bc568f4df78de4df29aad0d61e716a14780bff9f1ec3d021103ccd18d
2025-07-10 23:20:23,043 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b7d639ca7315bbed5c4acee019d63da6d3337f87e7143ae585ec7b0212c002e9
2025-07-10 23:20:23,046 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b7d639ca7315bbed5c4acee019d63da6d3337f87e7143ae585ec7b0212c002e9 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b7d639ca7315bbed5c4acee019d63da6d3337f87e7143ae585ec7b0212c002e9)
2025-07-11 02:34:29,167 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f5bad02cce0924841f875e3aed1dba2c6d13b60d22a52606b613edef3cf1b432
2025-07-11 02:34:29,170 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f5bad02cce0924841f875e3aed1dba2c6d13b60d22a52606b613edef3cf1b432 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f5bad02cce0924841f875e3aed1dba2c6d13b60d22a52606b613edef3cf1b432)
2025-07-11 03:11:04,886 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=89104ca36ab3330774a51b27bd6c1bc0970e27af4a1d13948d5af111a7a03dd8
2025-07-11 03:18:22,032 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a8eaf813c794210cd60cd3af9675e9c9739d6820d668e7b784ede19e0d130b54
2025-07-11 03:25:39,153 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=ce78ed2ed79add60cb03007dfcf1edd08c0008443197987d52946849311b746c
2025-07-11 03:37:16,821 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-11 03:37:17,236 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-11 06:48:02,016 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f42936322956453689fc7fc16de96472f4d6ceb68a1c5bc7d98110cad66b76ec
2025-07-11 06:48:02,019 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f42936322956453689fc7fc16de96472f4d6ceb68a1c5bc7d98110cad66b76ec (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f42936322956453689fc7fc16de96472f4d6ceb68a1c5bc7d98110cad66b76ec)
2025-07-11 06:50:09,619 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=54fbee12c0367a9c0217b0c36ea28b2044e2fb02e3a8527fe29c7fd4cf6f6c04
2025-07-11 06:50:09,621 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=54fbee12c0367a9c0217b0c36ea28b2044e2fb02e3a8527fe29c7fd4cf6f6c04 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=54fbee12c0367a9c0217b0c36ea28b2044e2fb02e3a8527fe29c7fd4cf6f6c04)
2025-07-11 06:57:34,113 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3e909f32be581acd855d6398764f022775e8a3c54315a85c246f70c5601d3126
2025-07-11 06:59:34,197 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-11 06:59:34,564 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-11 07:02:47,493 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=788b357e7f1f009a4d4f8c845790f8b1341219794def006f5b8c3ee1c0bb8649
2025-07-11 07:13:08,627 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8577f1619ae2215b5027f22b2f429bb76562ef4070e51274a3ed9dd354d1f828
2025-07-11 07:13:08,628 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8577f1619ae2215b5027f22b2f429bb76562ef4070e51274a3ed9dd354d1f828 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8577f1619ae2215b5027f22b2f429bb76562ef4070e51274a3ed9dd354d1f828)
2025-07-11 07:17:20,840 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d7e1cc2e6a8da9919efa63ea9e3175c666f5b9b19d0b56b219db27a54b62f537
2025-07-11 07:17:20,844 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d7e1cc2e6a8da9919efa63ea9e3175c666f5b9b19d0b56b219db27a54b62f537 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=d7e1cc2e6a8da9919efa63ea9e3175c666f5b9b19d0b56b219db27a54b62f537)
2025-07-11 07:18:25,772 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b0229553bb41a6c9afd7ffd04823fee8d7d35e24049337ae79f2a383947cabdb
2025-07-11 07:18:25,773 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b0229553bb41a6c9afd7ffd04823fee8d7d35e24049337ae79f2a383947cabdb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b0229553bb41a6c9afd7ffd04823fee8d7d35e24049337ae79f2a383947cabdb)
2025-07-11 07:30:24,461 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-11 07:30:24,853 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-11 08:12:41,265 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=1c47024259ad4eef82403d4e178168bc10690750530dab26b29f391bc8b8dd0a
2025-07-11 08:20:58,426 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf4faa4f82db635705f5a749c9249d930c33a47b15e1c93788d6007f4ef536ff
2025-07-11 08:20:58,431 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf4faa4f82db635705f5a749c9249d930c33a47b15e1c93788d6007f4ef536ff (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf4faa4f82db635705f5a749c9249d930c33a47b15e1c93788d6007f4ef536ff)
2025-07-11 08:50:10,965 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a7e326c125115a6a1005ef7558f86055fc215657497e12434f12de9222fcf885
2025-07-11 08:50:10,970 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a7e326c125115a6a1005ef7558f86055fc215657497e12434f12de9222fcf885 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a7e326c125115a6a1005ef7558f86055fc215657497e12434f12de9222fcf885)
2025-07-11 09:08:00,924 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=dc019fbe4b4c768777bf52c6b121a57b81db3f4d9c797588dad11d070973dd0c
2025-07-11 09:08:59,861 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c0a1a8791be73da18922c19991fe541661871351026dda94618cfe70e6efb5d
2025-07-11 09:08:59,875 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c0a1a8791be73da18922c19991fe541661871351026dda94618cfe70e6efb5d (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6c0a1a8791be73da18922c19991fe541661871351026dda94618cfe70e6efb5d)
2025-07-11 09:12:11,565 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=04e70cee91d9b3a079710f63adae7cba2e6d220b157bffdd54efad3601ba2de6
2025-07-11 09:12:11,569 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=04e70cee91d9b3a079710f63adae7cba2e6d220b157bffdd54efad3601ba2de6 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=04e70cee91d9b3a079710f63adae7cba2e6d220b157bffdd54efad3601ba2de6)
2025-07-11 09:14:19,791 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0410a24cc33f23cc36f9c892f5d8f927d1d481cf7532a52db375ff17fad9ae08
2025-07-11 09:14:19,793 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0410a24cc33f23cc36f9c892f5d8f927d1d481cf7532a52db375ff17fad9ae08 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=0410a24cc33f23cc36f9c892f5d8f927d1d481cf7532a52db375ff17fad9ae08)
2025-07-11 09:17:31,734 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=de94418e5cbbf52da20cfc7ba0a4d5ef46552f3580e2c5c0bea2727bef3873e6
2025-07-11 09:17:31,736 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=de94418e5cbbf52da20cfc7ba0a4d5ef46552f3580e2c5c0bea2727bef3873e6 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=de94418e5cbbf52da20cfc7ba0a4d5ef46552f3580e2c5c0bea2727bef3873e6)
2025-07-11 09:17:40,109 - handlers.discord.market.market_commands - WARNING - Interaction already expired for spread command by fantastic_beagle_96376
2025-07-11 09:18:40,252 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=50df9c8f0fd5fad993f2a166a0dbb33c559d3bc0bdfc33181785ba2cb4784b81
2025-07-11 09:19:40,045 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=1ed51dadc19e49a63bbb0f4b99187f87f73970df3181bda7b93f395dd4dc819c
2025-07-11 09:19:40,049 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=1ed51dadc19e49a63bbb0f4b99187f87f73970df3181bda7b93f395dd4dc819c (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=1ed51dadc19e49a63bbb0f4b99187f87f73970df3181bda7b93f395dd4dc819c)
2025-07-11 11:28:29,754 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 11:28:30,178 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 12:41:10,304 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=6e11f0da43970539e3dd741690297a78ebeb195b5c78e883732d545ac8449e92
2025-07-11 13:22:50,405 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=c1bcbf916742eef6ccdb86448d2743686fd34a7881723c827f09748bced1888d
2025-07-11 13:23:46,943 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a666540bab2873024786c0d9456042d2e325dc44599de347502d30fb8d3f86ef
2025-07-11 13:23:46,949 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a666540bab2873024786c0d9456042d2e325dc44599de347502d30fb8d3f86ef (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a666540bab2873024786c0d9456042d2e325dc44599de347502d30fb8d3f86ef)
2025-07-11 13:23:52,651 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7ccf79049f5698210cd79b109e2c7759166f771959b78d4dd12edace059e5d31
2025-07-11 13:24:51,498 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f97d79e9cce0fa9f618ece136758485ad1ef166939c0f5f5b6b24f102405c087
2025-07-11 13:24:51,505 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f97d79e9cce0fa9f618ece136758485ad1ef166939c0f5f5b6b24f102405c087 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=f97d79e9cce0fa9f618ece136758485ad1ef166939c0f5f5b6b24f102405c087)
2025-07-11 13:29:09,289 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=526aa88dbbf80315ed1f8380f508419ce7f9271b48ac71794d922f2660ae4738
2025-07-11 13:35:24,101 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=1d8c4d070fe92d67ef182fe5ce37da2d857f30c4080632f5cc2d02b8d5f8cc40
2025-07-11 13:52:35,327 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 13:52:36,071 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 14:02:52,107 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 14:02:52,459 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 14:08:47,684 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=165025721f4b9b2c91f46054242e81f0c7065c150c020c8369ec627e691835f4
2025-07-11 14:08:47,690 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=165025721f4b9b2c91f46054242e81f0c7065c150c020c8369ec627e691835f4 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=165025721f4b9b2c91f46054242e81f0c7065c150c020c8369ec627e691835f4)
2025-07-11 14:28:33,169 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 14:28:33,469 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 14:31:57,145 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=870c66d67f917c4f73c5a4473cd0543c4637daef9920c070968e4ed6587df765
2025-07-11 14:36:08,274 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-11 15:25:01,707 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 15:25:02,047 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 17:44:02,925 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 17:44:03,369 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 18:40:56,413 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: 
2025-07-11 18:46:13,107 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 18:46:13,539 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-11 21:36:59,566 - services.market.market_service - WARNING - Symbol 'WLDUSDT' not in supported mappings, using fallback: wld
2025-07-11 21:36:59,978 - services.market.market_service - WARNING - CoinGecko: Coin ID 'wld' not found (404)
2025-07-11 23:35:07,940 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-11 23:35:08,302 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-12 06:37:38,416 - services.market.mt5_data_service - ERROR - Error fetching real-time price for XAUUSD: Connection timeout to host https://query1.finance.yahoo.com/v8/finance/chart/GC=F
2025-07-12 07:19:48,774 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=bf37c60f7ba62de68488788500407c7419da60dea2d5b7e90a5595466cc47c97
2025-07-12 10:32:35,316 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7bb6539befb156d530e3279b5c48f9f5326b7698e6d4e741f42e79f606737724
2025-07-12 10:32:35,318 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7bb6539befb156d530e3279b5c48f9f5326b7698e6d4e741f42e79f606737724 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=7bb6539befb156d530e3279b5c48f9f5326b7698e6d4e741f42e79f606737724)
2025-07-12 14:47:17,448 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=a4ec5ae063cffb9f610f3129a6095393e993952ba11e64c1650a06fc9fc3a00b
2025-07-12 16:39:34,415 - services.market.economic_calendar_service - ERROR - Error fetching economic calendar: 
2025-07-12 18:01:19,372 - services.market.market_service - ERROR - [MARKET-SERVICE] CCXT error fetching prices for ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'NEARUSDT', 'LINKUSDT', 'ENAUSDT', 'DOGEUSDT', 'XRPUSDT', 'WLDUSDT']: binance GET https://fapi.binance.com/fapi/v1/ticker/24hr
2025-07-12 18:01:19,374 - services.market.market_service - WARNING - No price data received for watchlist symbols
2025-07-12 18:34:36,414 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-12 18:34:39,414 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-12 19:24:45,613 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=3c429d8b99d68c433132a60ddbe105302727a4874f33bb92d0616bb714c22e41
2025-07-12 19:31:00,295 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=8d72141f1636aa7a4470e0256aa06a0a5c09dc8c3682c53af9bb7200ea2b7586
2025-07-12 19:46:00,766 - services.market.short_term_price_alert_service - ERROR - Error fetching OHLCV data for BTCUSDT 1h: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1h&limit=2&symbol=BTCUSDT
2025-07-12 19:46:38,141 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=9eac24760d1397501e8180f6365f6b38d2c49e54994fd128fffe00789cb03147
2025-07-12 23:52:00,042 - services.market.market_service - WARNING - Symbol 'XRPUSDT' not in supported mappings, using fallback: xrp
2025-07-12 23:52:00,431 - services.market.market_service - WARNING - CoinGecko: Coin ID 'xrp' not found (404)
2025-07-13 00:53:13,418 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=03ef83d142750916de66b84842612ff6f1fed96ba23c9cf0b57141d1248d0079
2025-07-13 00:53:13,427 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=03ef83d142750916de66b84842612ff6f1fed96ba23c9cf0b57141d1248d0079 (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=03ef83d142750916de66b84842612ff6f1fed96ba23c9cf0b57141d1248d0079)
2025-07-13 02:12:28,150 - services.trading.trading_service - ERROR - Network error fetching balance: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b3053c0df56a3fd09b6bf4d76090596dfb49defdfa98c416a63eb3a38ff28bfb
2025-07-13 02:12:28,156 - services.core.error_service - WARNING - Retry 1/3 for get_account_balance: Network connection issue: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b3053c0df56a3fd09b6bf4d76090596dfb49defdfa98c416a63eb3a38ff28bfb (Caused by: NetworkError: binance GET https://fapi.binance.com/fapi/v3/account?timestamp=*************&recvWindow=10000&signature=b3053c0df56a3fd09b6bf4d76090596dfb49defdfa98c416a63eb3a38ff28bfb)
2025-07-13 03:52:54,956 - handlers.discord.market.watchlist_commands - ERROR - Error getting daily candle data for NEARUSDT: binance GET https://fapi.binance.com/fapi/v1/klines?interval=1d&limit=2&symbol=NEARUSDT
2025-07-13 04:00:54,912 - services.market.market_monitor_service - ERROR - Binance API error: 400 - {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-07-13 05:49:42,418 - services.market.economic_calendar_service - ERROR - Error fetching economic calendar: 
2025-07-13 12:45:06,434 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-13 12:45:09,414 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-13 13:15:53,893 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 10 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 756, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 122, in __lt__
    return self._when < other._when

2025-07-13 13:18:58,801 - discord.gateway - WARNING - Shard ID None heartbeat blocked for more than 20 seconds.
Loop thread traceback (most recent call last):
  File "/root/chartfix/bot.py", line 756, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 636, in run_until_complete
    self.run_forever()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 603, in run_forever
    self._run_once()
  File "/usr/lib/python3.10/asyncio/base_events.py", line 1909, in _run_once
    handle._run()
  File "/usr/lib/python3.10/asyncio/events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 688, in _network_loop_retry
    if not await action_cb():
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_updater.py", line 373, in polling_action_cb
    updates = await self.bot.get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 558, in get_updates
    updates = await super().get_updates(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 525, in decorator
    result = await func(self, *args, **kwargs)  # skipcq: PYL-E1102
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 3584, in get_updates
    await self._post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 613, in _post
    return await self._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/ext/_extbot.py", line 340, in _do_post
    return await super()._do_post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/_bot.py", line 641, in _do_post
    return await request.post(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 200, in post
    result = await self._request_wrapper(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_baserequest.py", line 330, in _request_wrapper
    code, payload = await self.do_request(
  File "/usr/local/lib/python3.10/dist-packages/telegram/request/_httpxrequest.py", line 268, in do_request
    res = await self._client.request(
  File "/usr/local/lib/python3.10/dist-packages/httpx/_client.py", line 1530, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
  File "/usr/local/lib/python3.10/dist-packages/httpx/_client.py", line 1625, in send
    await response.aread()
  File "/usr/local/lib/python3.10/dist-packages/httpx/_models.py", line 914, in aread
    self._content = b"".join([part async for part in self.aiter_bytes()])
  File "/usr/local/lib/python3.10/dist-packages/httpx/_models.py", line 914, in <listcomp>
    self._content = b"".join([part async for part in self.aiter_bytes()])
  File "/usr/local/lib/python3.10/dist-packages/httpx/_models.py", line 932, in aiter_bytes
    async for raw_bytes in self.aiter_raw():
  File "/usr/local/lib/python3.10/dist-packages/httpx/_models.py", line 990, in aiter_raw
    async for raw_stream_bytes in self.stream:
  File "/usr/local/lib/python3.10/dist-packages/httpx/_client.py", line 146, in __aiter__
    async for chunk in self._stream:
  File "/usr/local/lib/python3.10/dist-packages/httpx/_transports/default.py", line 249, in __aiter__
    async for part in self._httpcore_stream:
  File "/usr/local/lib/python3.10/dist-packages/httpcore/_async/connection_pool.py", line 403, in __aiter__
    async for part in self._stream:
  File "/usr/local/lib/python3.10/dist-packages/httpcore/_async/http11.py", line 334, in __aiter__
    async for chunk in self._connection._receive_response_body(**kwargs):
  File "/usr/local/lib/python3.10/dist-packages/httpcore/_async/http11.py", line 203, in _receive_response_body
    event = await self._receive_event(timeout=timeout)
  File "/usr/local/lib/python3.10/dist-packages/httpcore/_async/http11.py", line 214, in _receive_event
    event = self._h11_state.next_event()

2025-07-13 13:19:02,096 - services.trading.trade_h_service - ERROR - ❌ Error fetching Trade-H account balance: binance {"code":-1021,"msg":"Timestamp for this request is outside of the recvWindow."}
2025-07-13 13:21:30,469 - services.market.trading_time_service - ERROR - Error getting news trading opportunities: 'NoneType' object has no attribute 'fetch_economic_calendar'
2025-07-13 13:24:13,190 - __main__ - WARNING - Slow command execution: watchlist took 8.96s
2025-07-13 13:25:11,272 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 231 messages from #time-trade (keep_pinned: True)
2025-07-13 13:36:06,558 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 8 messages from #paxg (keep_pinned: False)
2025-07-13 13:48:44,124 - services.market.market_service - ERROR - Error fetching Fear & Greed Index: Connection timeout to host https://api.alternative.me/fng/
2025-07-13 13:56:41,255 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 4 messages from #paxg (keep_pinned: True)
2025-07-13 14:05:26,126 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 10 messages from #paxg (keep_pinned: False)
2025-07-13 14:07:45,190 - handlers.discord.admin.admin_commands - WARNING - Admin fantastic_beagle_96376 cleared 3 messages from #paxg (keep_pinned: True)
